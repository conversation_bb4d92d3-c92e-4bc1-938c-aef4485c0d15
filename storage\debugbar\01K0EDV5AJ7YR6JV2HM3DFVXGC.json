{"__meta": {"id": "01K0EDV5AJ7YR6JV2HM3DFVXGC", "datetime": "2025-07-18 09:29:00", "utime": **********.500619, "method": "GET", "uri": "/chatting/group/browser-unread-messages?_t=1752830938023", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[09:29:00] LOG.error: No application encryption key has been specified. {\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.431034, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752830938.396964, "end": **********.500664, "duration": 2.1036999225616455, "duration_str": "2.1s", "measures": [{"label": "Booting", "start": 1752830938.396964, "relative_start": 0, "end": **********.044263, "relative_end": **********.044263, "duration": 1.***************, "duration_str": "1.65s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.044286, "relative_start": 1.****************, "end": **********.500669, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "456ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.111936, "relative_start": 1.****************, "end": **********.130712, "relative_end": **********.130712, "duration": 0.*****************, "duration_str": "18.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.441136, "relative_start": 2.****************, "end": **********.494784, "relative_end": **********.494784, "duration": 0.*****************, "duration_str": "53.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "24MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Illuminate\\Encryption\\MissingAppKeyException", "message": "No application encryption key has been specified.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php", "line": 79, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:46</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"59 characters\">vendor/laravel/framework/src/Illuminate/Support/helpers.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>320</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Encryption\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Encryption\\EncryptionServiceProvider</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>77</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">tap</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>60</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">key</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Encryption\\EncryptionServiceProvider</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:16</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">SI-App</span>\"\n        \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Logo/si_app_logo.png</span>\"\n        \"<span class=sf-dump-key>favicon</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Logo/favicon.ico</span>\"\n        \"<span class=sf-dump-key>env</span>\" => \"<span class=sf-dump-str title=\"5 characters\">local</span>\"\n        \"<span class=sf-dump-key>debug</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">https://blueorange.test</span>\"\n        \"<span class=sf-dump-key>asset_url</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"3 characters\">UTC</span>\"\n        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        \"<span class=sf-dump-key>fallback_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        \"<span class=sf-dump-key>faker_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en_US</span>\"\n        \"<span class=sf-dump-key>key</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>cipher</span>\" => \"<span class=sf-dump-str title=\"11 characters\">AES-256-CBC</span>\"\n        \"<span class=sf-dump-key>maintenance</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>driver</span>\" => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>providers</span>\" => <span class=sf-dump-note>array:30</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\Auth\\AuthServiceProvider</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Bus\\BusServiceProvider</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Cache\\CacheServiceProvider</span>\"\n          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider</span>\"\n          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Cookie\\CookieServiceProvider</span>\"\n          <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Database\\DatabaseServiceProvider</span>\"\n          <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Encryption\\EncryptionServiceProvider</span>\"\n          <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Filesystem\\FilesystemServiceProvider</span>\"\n          <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"57 characters\">Illuminate\\Foundation\\Providers\\FoundationServiceProvider</span>\"\n          <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\Hashing\\HashServiceProvider</span>\"\n          <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\Mail\\MailServiceProvider</span>\"\n          <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"52 characters\">Illuminate\\Notifications\\NotificationServiceProvider</span>\"\n          <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Pagination\\PaginationServiceProvider</span>\"\n          <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Pipeline\\PipelineServiceProvider</span>\"\n          <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Queue\\QueueServiceProvider</span>\"\n          <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Redis\\RedisServiceProvider</span>\"\n          <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider</span>\"\n          <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"41 characters\">Illuminate\\Session\\SessionServiceProvider</span>\"\n          <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Translation\\TranslationServiceProvider</span>\"\n          <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Validation\\ValidationServiceProvider</span>\"\n          <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\View\\ViewServiceProvider</span>\"\n          <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"50 characters\">Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider</span>\"\n          <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"47 characters\">RealRashid\\SweetAlert\\SweetAlertServiceProvider</span>\"\n          <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"39 characters\">browner12\\helpers\\HelperServiceProvider</span>\"\n          <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"43 characters\">Spatie\\Permission\\PermissionServiceProvider</span>\"\n          <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"38 characters\">Maatwebsite\\Excel\\ExcelServiceProvider</span>\"\n          <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"32 characters\">App\\Providers\\AppServiceProvider</span>\"\n          <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"33 characters\">App\\Providers\\AuthServiceProvider</span>\"\n          <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"34 characters\">App\\Providers\\EventServiceProvider</span>\"\n          <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"34 characters\">App\\Providers\\RouteServiceProvider</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>aliases</span>\" => <span class=sf-dump-note>array:43</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>App</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Support\\Facades\\App</span>\"\n          \"<span class=sf-dump-key>Arr</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Illuminate\\Support\\Arr</span>\"\n          \"<span class=sf-dump-key>Artisan</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Support\\Facades\\Artisan</span>\"\n          \"<span class=sf-dump-key>Auth</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\Auth</span>\"\n          \"<span class=sf-dump-key>Blade</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Support\\Facades\\Blade</span>\"\n          \"<span class=sf-dump-key>Broadcast</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Support\\Facades\\Broadcast</span>\"\n          \"<span class=sf-dump-key>Bus</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Support\\Facades\\Bus</span>\"\n          \"<span class=sf-dump-key>Cache</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Support\\Facades\\Cache</span>\"\n          \"<span class=sf-dump-key>Config</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Support\\Facades\\Config</span>\"\n          \"<span class=sf-dump-key>Cookie</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Support\\Facades\\Cookie</span>\"\n          \"<span class=sf-dump-key>Crypt</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Support\\Facades\\Crypt</span>\"\n          \"<span class=sf-dump-key>Date</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\Date</span>\"\n          \"<span class=sf-dump-key>DB</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Support\\Facades\\DB</span>\"\n          \"<span class=sf-dump-key>Eloquent</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n          \"<span class=sf-dump-key>Event</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Support\\Facades\\Event</span>\"\n          \"<span class=sf-dump-key>File</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\File</span>\"\n          \"<span class=sf-dump-key>Gate</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\Gate</span>\"\n          \"<span class=sf-dump-key>Hash</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\Hash</span>\"\n          \"<span class=sf-dump-key>Http</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\Http</span>\"\n          \"<span class=sf-dump-key>Js</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Illuminate\\Support\\Js</span>\"\n          \"<span class=sf-dump-key>Lang</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\Lang</span>\"\n          \"<span class=sf-dump-key>Log</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Support\\Facades\\Log</span>\"\n          \"<span class=sf-dump-key>Mail</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\Mail</span>\"\n          \"<span class=sf-dump-key>Notification</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Support\\Facades\\Notification</span>\"\n          \"<span class=sf-dump-key>Number</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Support\\Number</span>\"\n          \"<span class=sf-dump-key>Password</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\Support\\Facades\\Password</span>\"\n          \"<span class=sf-dump-key>Process</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Support\\Facades\\Process</span>\"\n          \"<span class=sf-dump-key>Queue</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Support\\Facades\\Queue</span>\"\n          \"<span class=sf-dump-key>RateLimiter</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\Support\\Facades\\RateLimiter</span>\"\n          \"<span class=sf-dump-key>Redirect</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\Support\\Facades\\Redirect</span>\"\n          \"<span class=sf-dump-key>Request</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Support\\Facades\\Request</span>\"\n          \"<span class=sf-dump-key>Response</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\Support\\Facades\\Response</span>\"\n          \"<span class=sf-dump-key>Route</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Support\\Facades\\Route</span>\"\n          \"<span class=sf-dump-key>Schema</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Support\\Facades\\Schema</span>\"\n          \"<span class=sf-dump-key>Session</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Support\\Facades\\Session</span>\"\n          \"<span class=sf-dump-key>Storage</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Support\\Facades\\Storage</span>\"\n          \"<span class=sf-dump-key>Str</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Illuminate\\Support\\Str</span>\"\n          \"<span class=sf-dump-key>URL</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Support\\Facades\\URL</span>\"\n          \"<span class=sf-dump-key>Validator</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Support\\Facades\\Validator</span>\"\n          \"<span class=sf-dump-key>View</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\View</span>\"\n          \"<span class=sf-dump-key>Vite</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\Vite</span>\"\n          \"<span class=sf-dump-key>Alert</span>\" => \"<span class=sf-dump-str title=\"35 characters\">RealRashid\\SweetAlert\\Facades\\Alert</span>\"\n          \"<span class=sf-dump-key>Excel</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Maatwebsite\\Excel\\Facades\\Excel</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>32</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">parseKey</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Encryption\\EncryptionServiceProvider</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:16</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">SI-App</span>\"\n        \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Logo/si_app_logo.png</span>\"\n        \"<span class=sf-dump-key>favicon</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Logo/favicon.ico</span>\"\n        \"<span class=sf-dump-key>env</span>\" => \"<span class=sf-dump-str title=\"5 characters\">local</span>\"\n        \"<span class=sf-dump-key>debug</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">https://blueorange.test</span>\"\n        \"<span class=sf-dump-key>asset_url</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"3 characters\">UTC</span>\"\n        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        \"<span class=sf-dump-key>fallback_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        \"<span class=sf-dump-key>faker_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en_US</span>\"\n        \"<span class=sf-dump-key>key</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>cipher</span>\" => \"<span class=sf-dump-str title=\"11 characters\">AES-256-CBC</span>\"\n        \"<span class=sf-dump-key>maintenance</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>driver</span>\" => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>providers</span>\" => <span class=sf-dump-note>array:30</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\Auth\\AuthServiceProvider</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Bus\\BusServiceProvider</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Cache\\CacheServiceProvider</span>\"\n          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider</span>\"\n          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Cookie\\CookieServiceProvider</span>\"\n          <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Database\\DatabaseServiceProvider</span>\"\n          <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Encryption\\EncryptionServiceProvider</span>\"\n          <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Filesystem\\FilesystemServiceProvider</span>\"\n          <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"57 characters\">Illuminate\\Foundation\\Providers\\FoundationServiceProvider</span>\"\n          <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\Hashing\\HashServiceProvider</span>\"\n          <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\Mail\\MailServiceProvider</span>\"\n          <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"52 characters\">Illuminate\\Notifications\\NotificationServiceProvider</span>\"\n          <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Pagination\\PaginationServiceProvider</span>\"\n          <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Pipeline\\PipelineServiceProvider</span>\"\n          <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Queue\\QueueServiceProvider</span>\"\n          <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Redis\\RedisServiceProvider</span>\"\n          <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider</span>\"\n          <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"41 characters\">Illuminate\\Session\\SessionServiceProvider</span>\"\n          <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Translation\\TranslationServiceProvider</span>\"\n          <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Validation\\ValidationServiceProvider</span>\"\n          <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\View\\ViewServiceProvider</span>\"\n          <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"50 characters\">Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider</span>\"\n          <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"47 characters\">RealRashid\\SweetAlert\\SweetAlertServiceProvider</span>\"\n          <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"39 characters\">browner12\\helpers\\HelperServiceProvider</span>\"\n          <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"43 characters\">Spatie\\Permission\\PermissionServiceProvider</span>\"\n          <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"38 characters\">Maatwebsite\\Excel\\ExcelServiceProvider</span>\"\n          <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"32 characters\">App\\Providers\\AppServiceProvider</span>\"\n          <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"33 characters\">App\\Providers\\AuthServiceProvider</span>\"\n          <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"34 characters\">App\\Providers\\EventServiceProvider</span>\"\n          <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"34 characters\">App\\Providers\\RouteServiceProvider</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>aliases</span>\" => <span class=sf-dump-note>array:43</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>App</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Support\\Facades\\App</span>\"\n          \"<span class=sf-dump-key>Arr</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Illuminate\\Support\\Arr</span>\"\n          \"<span class=sf-dump-key>Artisan</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Support\\Facades\\Artisan</span>\"\n          \"<span class=sf-dump-key>Auth</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\Auth</span>\"\n          \"<span class=sf-dump-key>Blade</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Support\\Facades\\Blade</span>\"\n          \"<span class=sf-dump-key>Broadcast</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Support\\Facades\\Broadcast</span>\"\n          \"<span class=sf-dump-key>Bus</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Support\\Facades\\Bus</span>\"\n          \"<span class=sf-dump-key>Cache</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Support\\Facades\\Cache</span>\"\n          \"<span class=sf-dump-key>Config</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Support\\Facades\\Config</span>\"\n          \"<span class=sf-dump-key>Cookie</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Support\\Facades\\Cookie</span>\"\n          \"<span class=sf-dump-key>Crypt</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Support\\Facades\\Crypt</span>\"\n          \"<span class=sf-dump-key>Date</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\Date</span>\"\n          \"<span class=sf-dump-key>DB</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Support\\Facades\\DB</span>\"\n          \"<span class=sf-dump-key>Eloquent</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n          \"<span class=sf-dump-key>Event</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Support\\Facades\\Event</span>\"\n          \"<span class=sf-dump-key>File</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\File</span>\"\n          \"<span class=sf-dump-key>Gate</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\Gate</span>\"\n          \"<span class=sf-dump-key>Hash</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\Hash</span>\"\n          \"<span class=sf-dump-key>Http</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\Http</span>\"\n          \"<span class=sf-dump-key>Js</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Illuminate\\Support\\Js</span>\"\n          \"<span class=sf-dump-key>Lang</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\Lang</span>\"\n          \"<span class=sf-dump-key>Log</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Support\\Facades\\Log</span>\"\n          \"<span class=sf-dump-key>Mail</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\Mail</span>\"\n          \"<span class=sf-dump-key>Notification</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Support\\Facades\\Notification</span>\"\n          \"<span class=sf-dump-key>Number</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Support\\Number</span>\"\n          \"<span class=sf-dump-key>Password</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\Support\\Facades\\Password</span>\"\n          \"<span class=sf-dump-key>Process</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Support\\Facades\\Process</span>\"\n          \"<span class=sf-dump-key>Queue</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Support\\Facades\\Queue</span>\"\n          \"<span class=sf-dump-key>RateLimiter</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\Support\\Facades\\RateLimiter</span>\"\n          \"<span class=sf-dump-key>Redirect</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\Support\\Facades\\Redirect</span>\"\n          \"<span class=sf-dump-key>Request</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Support\\Facades\\Request</span>\"\n          \"<span class=sf-dump-key>Response</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\Support\\Facades\\Response</span>\"\n          \"<span class=sf-dump-key>Route</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Support\\Facades\\Route</span>\"\n          \"<span class=sf-dump-key>Schema</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Support\\Facades\\Schema</span>\"\n          \"<span class=sf-dump-key>Session</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Support\\Facades\\Session</span>\"\n          \"<span class=sf-dump-key>Storage</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Support\\Facades\\Storage</span>\"\n          \"<span class=sf-dump-key>Str</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Illuminate\\Support\\Str</span>\"\n          \"<span class=sf-dump-key>URL</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Support\\Facades\\URL</span>\"\n          \"<span class=sf-dump-key>Validator</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Support\\Facades\\Validator</span>\"\n          \"<span class=sf-dump-key>View</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\View</span>\"\n          \"<span class=sf-dump-key>Vite</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Support\\Facades\\Vite</span>\"\n          \"<span class=sf-dump-key>Alert</span>\" => \"<span class=sf-dump-str title=\"35 characters\">RealRashid\\SweetAlert\\Facades\\Alert</span>\"\n          \"<span class=sf-dump-key>Excel</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Maatwebsite\\Excel\\Facades\\Excel</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>908</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Encryption\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Encryption\\EncryptionServiceProvider</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Foundation\\Application]</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>795</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">build</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>986</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">encrypter</span>\"\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>731</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">encrypter</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>971</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">encrypter</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1066</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">encrypter</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>982</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">resolveClass</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">[object ReflectionParameter]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>943</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">resolveDependencies</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>ReflectionParameter</span> {<a class=sf-dump-ref>#948</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"9 characters\">encrypter</span>\"\n          <span class=sf-dump-meta>position</span>: <span class=sf-dump-num>0</span>\n          <span class=sf-dump-meta>typeHint</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Illuminate\\Contracts\\Encryption\\Encrypter\n41 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Contracts\\Encryption</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">Encrypter</span></span>\"\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>795</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">build</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">App\\Http\\Middleware\\EncryptCookies</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>986</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">App\\Http\\Middleware\\EncryptCookies</span>\"\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>731</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">App\\Http\\Middleware\\EncryptCookies</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>971</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">App\\Http\\Middleware\\EncryptCookies</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>172</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">App\\Http\\Middleware\\EncryptCookies</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>805</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>784</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>748</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>737</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>99</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["    {\n", "        return tap($config['key'], function ($key) {\n", "            if (empty($key)) {\n", "                throw new Missing<PERSON><PERSON><PERSON>eyException;\n", "            }\n", "        });\n", "    }\n"], "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FEncryption%2FEncryptionServiceProvider.php&line=79", "ajax": false, "filename": "EncryptionServiceProvider.php", "line": "79"}}]}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET chatting/group/browser-unread-messages", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip", "controller": "App\\Http\\Controllers\\Administration\\Chatting\\GroupChattingController@fetchUnreadMessagesForBrowser<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FChatting%2FGroupChattingController.php&line=87\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "administration.chatting.group.browser.fetch_unread", "prefix": "chatting/group", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FChatting%2FGroupChattingController.php&line=87\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Chatting/GroupChattingController.php:87-139</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"data": {"status": "500 Internal Server Error", "full_url": "https://blueorange.test/chatting/group/browser-unread-messages?_t=1752830938023", "action_name": "administration.chatting.group.browser.fetch_unread", "controller_action": "App\\Http\\Controllers\\Administration\\Chatting\\GroupChattingController@fetchUnreadMessagesForBrowser", "uri": "GET chatting/group/browser-unread-messages", "controller": "App\\Http\\Controllers\\Administration\\Chatting\\GroupChattingController@fetchUnreadMessagesForBrowser<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FChatting%2FGroupChattingController.php&line=87\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "chatting/group", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FChatting%2FGroupChattingController.php&line=87\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Chatting/GroupChattingController.php:87-139</a>", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip", "duration": "2.15s", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1043773521 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_t</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1752830938023</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043773521\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-734568419 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-734568419\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-92815830 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://blueorange.test/task/all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6IldYNnllczlCMWJUQllHdWtFdkJJbnc9PSIsInZhbHVlIjoiaVFXU3RwR1I5UDhyR3pRc25XU1ZwaTdJTUhUMVA0d1p5TmRPd2w5NTBCUTNaRGtDNWpQazNtS0tMdUZDUHJ0a0xZZllvZ2pHTnpqeE44OWdLeTJpbzZld0ZQT1k5UlJuTW91K1IwUEduSmZ1Nlpsa3NBVjRML3QzdFY5Y0pkbDUiLCJtYWMiOiJhNTIxNWM1MWI0YmJkZjEzMjY4NjljNzM2OWY2NjY3OTk1Njc2NDg5MjFmNmZhZTQwNzJlODk2ZTVmNDJiY2VkIiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6ImI4V1duR3BhRlo1T05Fd1dzcWpSaXc9PSIsInZhbHVlIjoiZW5vazdvdVVaOTVxNXNSZXVyVkhWanNPZGgzUUNIZXM4bVI2cFRtNFZsbVFzVFBnQmtLeFNMS0E0THBtODVMb0pib01IOVBHT2FleUZYK2c4emZpMDhlS2ZQbkM5MFFQbXptMlQ2MzdiUnNTV3lMeHF4M1NNdUNFajRSNUlSVXoiLCJtYWMiOiJlOTcwZDE3YmE2MzZmODUzN2NkNTY2M2U2NzFjMWQ1ZTUyM2FmOTEyYWFmZGQ5NmRmNzM5N2Q3NzNkMmMxNWExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-92815830\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-794261220 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IldYNnllczlCMWJUQllHdWtFdkJJbnc9PSIsInZhbHVlIjoiaVFXU3RwR1I5UDhyR3pRc25XU1ZwaTdJTUhUMVA0d1p5TmRPd2w5NTBCUTNaRGtDNWpQazNtS0tMdUZDUHJ0a0xZZllvZ2pHTnpqeE44OWdLeTJpbzZld0ZQT1k5UlJuTW91K1IwUEduSmZ1Nlpsa3NBVjRML3QzdFY5Y0pkbDUiLCJtYWMiOiJhNTIxNWM1MWI0YmJkZjEzMjY4NjljNzM2OWY2NjY3OTk1Njc2NDg5MjFmNmZhZTQwNzJlODk2ZTVmNDJiY2VkIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImI4V1duR3BhRlo1T05Fd1dzcWpSaXc9PSIsInZhbHVlIjoiZW5vazdvdVVaOTVxNXNSZXVyVkhWanNPZGgzUUNIZXM4bVI2cFRtNFZsbVFzVFBnQmtLeFNMS0E0THBtODVMb0pib01IOVBHT2FleUZYK2c4emZpMDhlS2ZQbkM5MFFQbXptMlQ2MzdiUnNTV3lMeHF4M1NNdUNFajRSNUlSVXoiLCJtYWMiOiJlOTcwZDE3YmE2MzZmODUzN2NkNTY2M2U2NzFjMWQ1ZTUyM2FmOTEyYWFmZGQ5NmRmNzM5N2Q3NzNkMmMxNWExIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-794261220\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1894734109 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 09:29:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1894734109\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-730219832 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-730219832\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "https://blueorange.test/chatting/group/browser-unread-messages?_t=1752830938023", "action_name": "administration.chatting.group.browser.fetch_unread", "controller_action": "App\\Http\\Controllers\\Administration\\Chatting\\GroupChattingController@fetchUnreadMessagesForBrowser"}, "badge": "500 Internal Server Error"}}