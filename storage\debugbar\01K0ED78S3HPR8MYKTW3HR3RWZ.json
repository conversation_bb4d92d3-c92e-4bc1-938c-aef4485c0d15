{"__meta": {"id": "01K0ED78S3HPR8MYKTW3HR3RWZ", "datetime": "2025-07-18 15:18:08", "utime": **********.678216, "method": "GET", "uri": "/password/reset", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752830287.713616, "end": **********.678255, "duration": 0.9646391868591309, "duration_str": "965ms", "measures": [{"label": "Booting", "start": 1752830287.713616, "relative_start": 0, "end": **********.295012, "relative_end": **********.295012, "duration": 0.****************, "duration_str": "581ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.295029, "relative_start": 0.****************, "end": **********.678258, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.314257, "relative_start": 0.****************, "end": **********.319181, "relative_end": **********.319181, "duration": 0.*****************, "duration_str": "4.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.340899, "relative_start": 0.****************, "end": **********.670054, "relative_end": **********.670054, "duration": 0.*****************, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "1x auth.passwords.email", "param_count": null, "params": [], "start": **********.345502, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/auth/passwords/email.blade.phpauth.passwords.email", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fauth%2Fpasswords%2Femail.blade.php&line=1", "ajax": false, "filename": "email.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.passwords.email"}, {"name": "1x sweetalert::alert", "param_count": null, "params": [], "start": **********.668167, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/vendor/sweetalert/alert.blade.phpsweetalert::alert", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fvendor%2Fsweetalert%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "sweetalert::alert"}]}, "route": {"uri": "GET password/reset", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\ForgotPasswordController@showLinkRequestForm<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fui%2Fauth-backend%2FSendsPasswordResetEmails.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "as": "password.request", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fui%2Fauth-backend%2FSendsPasswordResetEmails.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/ui/auth-backend/SendsPasswordResetEmails.php:17-20</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fGZzorvexEjmiDTxvUiHhuuCf3dW0TPauGpuWwK3", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "https://blueorange.test/password/reset", "action_name": "password.request", "controller_action": "App\\Http\\Controllers\\Auth\\ForgotPasswordController@showLinkRequestForm", "uri": "GET password/reset", "controller": "App\\Http\\Controllers\\Auth\\ForgotPasswordController@showLinkRequestForm<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fui%2Fauth-backend%2FSendsPasswordResetEmails.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fui%2Fauth-backend%2FSendsPasswordResetEmails.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/ui/auth-backend/SendsPasswordResetEmails.php:17-20</a>", "middleware": "web", "duration": "965ms", "peak_memory": "26MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-144105721 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-144105721\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-8459125 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-8459125\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1724328725 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">https://blueorange.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6Im9IdGdWS2ZyV3VJcVFBZ2tXcUZJMHc9PSIsInZhbHVlIjoiZlJFV1BEV2V6aFpJMG9Tb01lcDVPMmh5eWRJZkxUWUVYK2N5anhueVo0RmJUcjE3OVJIdkJEUC9kaVRyRVZOVlJIVmQvVzlLYkttZmsrVElxSnpCMTk4T0hMYVBiNVZOeGNKMzVKL1JrL1RFM0t2ZnZZSVpaQ0ZMeFgxVlJlcVEiLCJtYWMiOiIwZTdmNGIzMGY4YjdiYjVkZmFiNDNmYWJkOGEyYWFkMTYyMTYwYWI0ZTFiYzBhMmQxZmY4ZjIwNTA2NzM5MGQ4IiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6IlptdG4wZ1pWUlJoenN0SExoS1A3YUE9PSIsInZhbHVlIjoiMlVtYU1uY0ZOTzVvOEc5OThmbEg0azd4eE0wcWdvQ3BlVzcrbjlEd0FEVTJnL2tEcFpCUHd2TGN5MzJuUGJUZkVzdVBCakFZN0xrY2pnZHplTTh6QzMyM2k3UlFEWjNPL3BSOXk3NHRLM0IxYjgyOW9wcDZhaThtM1N6azJXMFMiLCJtYWMiOiIxMWQ1ODQyOGZlMjgxMTBkNjg1NjE5OGY1NWFkZTM5ZTU1ZDBmMzdjNjA1YzQ1NmExOWY4Nzc0MTFiZTRkNGVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1724328725\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-488242642 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fGZzorvexEjmiDTxvUiHhuuCf3dW0TPauGpuWwK3</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5nOfSePvXt7GFN3GHaculTynrtV19CdhvdQScxLV</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-488242642\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2016415256 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 09:18:08 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2016415256\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-395761564 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fGZzorvexEjmiDTxvUiHhuuCf3dW0TPauGpuWwK3</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">https://blueorange.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-395761564\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://blueorange.test/password/reset", "action_name": "password.request", "controller_action": "App\\Http\\Controllers\\Auth\\ForgotPasswordController@showLinkRequestForm"}, "badge": null}}